<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <LinearLayout
        android:id="@+id/left_chat_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginEnd="4dp"
        android:orientation="vertical">

<!--        <LinearLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="4dp"-->
<!--            android:gravity="center"-->
<!--            android:orientation="horizontal">-->

<!--            <ImageView-->
<!--                android:id="@+id/ai_icon"-->
<!--                android:layout_width="16dp"-->
<!--                android:layout_height="16dp"-->
<!--                android:src="@drawable/svg_ic_tab_ai_chat"-->
<!--                app:tint="?colorPrimary" />-->

<!--            <TextView-->
<!--                android:id="@+id/ai_name"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginHorizontal="4dp"-->
<!--                android:text="ChatGPT"-->
<!--                android:textColor="@color/clr_grey" />-->

<!--            <ImageView-->
<!--                android:layout_width="16dp"-->
<!--                android:layout_height="16dp"-->
<!--                android:src="@drawable/svg_ic_arrow_down"-->
<!--                android:visibility="invisible"-->
<!--                app:tint="?themeTextColor" />-->
<!--        </LinearLayout>-->

        <com.google.android.material.card.MaterialCardView
            style="@style/CardBubbleReceiveBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardPreventCornerOverlap="false"
            app:cardUseCompatPadding="false">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/left_chat_model"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:adjustViewBounds="true"
                        android:scaleType="centerCrop"
                        android:layout_gravity="bottom"
                        android:src="@drawable/ic_chat_gpt"
                        app:shapeAppearanceOverlay="@style/CircleImageView" />

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/animationView"
                        android:layout_width="60dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="8dp"
                        app:lottie_autoPlay="false"
                        app:lottie_loop="true"
                        app:lottie_rawRes="@raw/loadingchat"
                        android:layout_gravity="bottom"/>
                    <LinearLayout
                        android:id="@+id/left_chat_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/receive_round_box"
                        android:paddingStart="5dp"
                        android:orientation="horizontal"
                        tools:padding="20dp">
                        <WebView
                            android:id="@+id/left_chat_text_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="8dp"
                            tools:ignore="WebViewLayout" />

                        <TextView
                            android:id="@+id/left_chat_text_error"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="8dp"
                            android:fontFamily="@font/manrope_medium"
                            android:textColor="#C80815"
                            android:textIsSelectable="true"
                            android:textSize="16sp"
                            android:visibility="gone"
                            tools:text="@string/dummy_text"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/copy_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="end"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:contentDescription="@string/copy"
                        android:src="@drawable/ic_copy"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="8dp"
                        android:textSize="@dimen/dimen_text_size_12"
                        android:fontFamily="@font/manrope_regular"
                        android:textColor="#C0C5CA"
                        android:text="@string/copy"/>
                </LinearLayout>
            </LinearLayout>
            <ImageView
                android:id="@+id/resend_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/svg_ic_back"
                android:visibility="gone"
                android:layout_margin="8dp"
                android:layout_gravity="center"
                tools:visibility="visible" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/right_chat_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="20dp"
        android:gravity="end"
        android:orientation="vertical">

<!--        <LinearLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="4dp"-->
<!--            android:gravity="center"-->
<!--            android:orientation="horizontal">-->

<!--            <ImageView-->
<!--                android:layout_width="16dp"-->
<!--                android:layout_height="16dp"-->
<!--                android:src="@drawable/svg_ic_education" />-->

<!--            <TextView-->
<!--                android:id="@+id/you_name"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginStart="4dp"-->
<!--                android:text="@string/txtid_you"-->
<!--                android:textColor="@color/clr_grey" />-->
<!--        </LinearLayout>-->


        <com.google.android.material.card.MaterialCardView
            style="@style/CardBubbleSentBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardPreventCornerOverlap="false"
            app:cardUseCompatPadding="false"
            tools:visibility="gone">

            <LinearLayout
                android:id="@+id/right_chat_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/send_round_box"
                android:orientation="vertical"
                android:paddingEnd="5dp"
                tools:padding="20dp">

                <androidx.cardview.widget.CardView
                    android:id="@+id/roundedCornerCardView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:visibility="gone"
                    app:cardCornerRadius="4dp"
                    tools:visibility="visible">

                    <ImageView
                        android:id="@+id/roundedCornerImageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="4dp"
                        android:adjustViewBounds="true"
                        android:scaleType="fitCenter"
                        tools:src="@drawable/permissionx_ic_setting" />
                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/right_chat_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:fontFamily="@font/roboto_regular"
                    android:textColor="@color/clr_white"
                    android:textIsSelectable="true"
                    android:textSize="16sp"
                    tools:text="@string/dummy_text" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</RelativeLayout>